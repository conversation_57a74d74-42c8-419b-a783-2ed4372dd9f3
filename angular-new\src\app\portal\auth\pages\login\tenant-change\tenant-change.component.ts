import { NgIf } from '@angular/common';
import { Component, effect, inject, signal } from '@angular/core';
import { ITenantLoginInfoDto } from '@portal/auth/entities/autenticacao';
import {
    GenericFormModalComponent,
    IFormField,
} from '@shared/components/generic';
import { AutenticacaoController } from '@portal/auth/controller/autenticacao.controller';
import { SessionStorageService } from '@shared/service/sessionStorage.service';

@Component({
    selector: 'app-tenant-change',
    imports: [NgIf, GenericFormModalComponent],
    standalone: true,
    templateUrl: './tenant-change.component.html',
    styleUrl: './tenant-change.component.scss',
})
export class TenantChangeComponent {
    autenticacaoController = inject(AutenticacaoController);
    private sessionStorageService = inject(SessionStorageService);
    tenant: ITenantLoginInfoDto | null = null;
    modalChangeTenant = signal(false);
    saving: boolean = false;

    formFields: IFormField[] = [
        {
            name: 'identificador',
            label: 'Identificador',
            type: 'text',
            placeholder: 'Digite a tenant',
            col: 12,
        },
    ];

    constructor() {
        effect(() => {
            this.tenant =
                this.autenticacaoController.currentLoginInformations()
                    ?.tenant || null;
            this.modalChangeTenant.set(false);
            if (this.tenant) {
                this.formFields[0].defaultValue = this.tenant.tenancyName;
            }
        });
    }

    onFormCancel() {
        this.modalChangeTenant.set(false);
    }

    onFormSubmit(formData: any) {
        if (!formData.identificador) {
            this.sessionStorageService.removeItem('tenantId');
            location.reload();
            return;
        }

        this.saving = true;
        try {
            this.autenticacaoController.isTenantAvailable(
                formData.identificador,
                (mensagemErro) => this.errorDefinirTenant(mensagemErro)
            );
        } catch (error) {
            alert('Erro ao selecionar o tenant');
        }
        this.saving = false;
    }

    errorDefinirTenant(mensagem: string) {
        this.saving = false;
        alert(mensagem);
    }
}
