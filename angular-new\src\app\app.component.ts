import { Component, inject } from '@angular/core';
import { CommonModule, NgClass, ViewportScroller } from '@angular/common';
import { RouterOutlet, Router, Event, NavigationEnd } from '@angular/router';
import { SidebarComponent } from '@portal/layout/components/sidebar/sidebar.component';
import { HeaderComponent } from '@portal/layout/components/header/header.component';
import { FooterComponent } from '@portal/layout/components/footer/footer.component';
import { ThemeSettingsController } from '@portal/layout/controller/themeSettings.controller';
import { LayoutController } from '@portal/layout/controller/layout.controller';
import { MessageController } from '@shared/controller/message.controller';
import { SuccessModalComponent } from '@shared/components/toasts/success-modal/success-modal.component';

@Component({
    selector: 'app-root',
    imports: [
        RouterOutlet,
        CommonModule,
        SidebarComponent,
        HeaderComponent,
        FooterComponent,
        NgClass,
        SuccessModalComponent,
    ],
    templateUrl: './app.component.html',
    styleUrl: './app.component.scss',
})
export class AppComponent {
    // Title
    title = 'REPO ESTRUTURA';

    // isSidebarToggled
    isSidebarToggled = false;

    // Message Controller
    messageController = inject(MessageController);

    constructor(
        public router: Router,
        private layoutController: LayoutController,
        private viewportScroller: ViewportScroller,
        public themeSettingsController: ThemeSettingsController
    ) {
        this.router.events.subscribe((event: Event) => {
            if (event instanceof NavigationEnd) {
                // Scroll to the top after each navigation end
                this.viewportScroller.scrollToPosition([0, 0]);
            }
        });
        this.layoutController.isSidebarToggled$.subscribe(
            (isSidebarToggled) => {
                this.isSidebarToggled = isSidebarToggled;
            }
        );
    }

    isBlankPageRoute(url: string): boolean {
        return (
            url === '/' ||
            url.startsWith('/conta/') ||
            url.startsWith('/components/') ||
            url === '/conta' ||
            url === '/coming-soon'
        );
    }
}
