<div
    class="custom-modal-overlay"
    *ngIf="isOpen()"
    (click)="onOverlayClick($event)"
>
    <div class="custom-modal-container">
        <div class="custom-modal-content">
            <div class="custom-icon">
                <div class="icon-container">
                    <div
                        class="icon-circle flex justify-center items-center"
                        [style.border-color]="iconColor"
                        [style.background-color]="iconColor"
                    >
                        <i
                            class="material-symbols-outlined text-white text-5xl!"
                            >{{ config.icon }}</i
                        >
                    </div>
                </div>
            </div>
            <div class="custom-message">
                <h3 [style.color]="titleColor">{{ config.title }}</h3>
                <p>{{ config.message }}</p>
            </div>
        </div>
    </div>
</div>
