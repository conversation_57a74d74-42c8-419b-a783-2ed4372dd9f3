import { Injectable, inject, signal, computed, effect } from '@angular/core';
import { Router } from '@angular/router';
import { Location } from '@angular/common';

import { AutenticacaoService } from '../service/autenticacao.service';
import { SessionStorageService } from '@shared/service/sessionStorage.service';
import { Utils } from '@shared/utils/shared.utils';
import { MessageController } from '@shared/controller/message.controller';
import {
    IAuthenticateModel,
    IAuthState,
    IGetCurrentLoginInformationsOutput,
    IPermissionListResultDto,
    ModuleAccessToken,
} from '../entities/autenticacao';
import { EnumTenantAvailabilityState } from '../entities/autenticacao.enum';
import { AbpResponse } from '@shared/entities/abp-response';

@Injectable({ providedIn: 'root' })
export class AutenticacaoController {
    private readonly autenticacaoService = inject(AutenticacaoService);
    private readonly sessionStorage = inject(SessionStorageService);
    private readonly utils = inject(Utils);
    private readonly router = inject(Router);
    private readonly location = inject(Location);
    private readonly messageController = inject(MessageController);

    private readonly _stateLogin = signal<IAuthState>({
        email: '',
        pin: undefined,
        tenantId: undefined,
        isLoginAdmin: false,
        isLoginMFA: false,
        validEmail: false,
        loading: false,
    });
    private readonly _accessTokens = signal<ModuleAccessToken[]>([]);
    private readonly _currentLoginInformations =
        signal<IGetCurrentLoginInformationsOutput | null>(null);
    private readonly _permissions = signal<IPermissionListResultDto | null>(
        null
    );
    private readonly _authResolved = signal<boolean>(false);

    readonly stateLogin = computed(() => this._stateLogin());
    readonly isLoading = computed(() => this._stateLogin().loading);
    readonly accessTokens = computed(() => this._accessTokens());
    readonly permissions = computed(() => this._permissions());
    readonly authResolved = computed(() => this._authResolved());
    readonly currentLoginInformations = computed(() =>
        this._currentLoginInformations()
    );

    constructor() {
        this.initialize();
    }

    private async initialize(): Promise<void> {
        const url = this.location.path();
        const isLoginRoute = this.isLoginRoute(url);

        if (isLoginRoute) {
            this.sessionStorage.removeItem('accessTokens');
            const tenantId = this.sessionStorage.getItem('tenantId');
            if (tenantId) await this.setCurrentLoginInformations();
            else this.sessionStorage.removeItem('tenantId');
            return;
        }

        const tokens = this.restoreTokens();
        const loginInfo = this.restoreLoginInfo();
        const permissions = this.restorePermissions();

        if (
            !this._authResolved() &&
            (!tokens.length || !loginInfo || !permissions)
        ) {
            this.logout();
            return;
        }

        this._accessTokens.set(tokens);
        this._currentLoginInformations.set(loginInfo);
        this._permissions.set(permissions);
        this._authResolved.set(true);
    }

    private restoreTokens(): ModuleAccessToken[] {
        const tokens = this.sessionStorage.getItem('accessTokens');
        return tokens ? JSON.parse(tokens) : [];
    }

    private restoreLoginInfo(): IGetCurrentLoginInformationsOutput | null {
        const info = this.sessionStorage.getItem('currentLoginInformations');
        return info ? JSON.parse(info) : null;
    }

    private restorePermissions(): IPermissionListResultDto | null {
        const permissions = this.sessionStorage.getItem('permissions');
        return permissions ? JSON.parse(permissions) : null;
    }

    async verifyEmail(
        email: string,
        loginAdmin: boolean,
        errorCallback: (msg: string) => void = () => {}
    ): Promise<void> {
        try {
            this.sessionStorage.removeItem('accessTokens');
            const response = await this.autenticacaoService.GetLoginInformation(
                email,
                loginAdmin
            );

            this._stateLogin.set({
                ...this._stateLogin(),
                email,
                isLoginAdmin: loginAdmin,
                loading: true,
                validEmail: true,
            });

            if (response?.customAuth == true && response?.ssoAuthAddress) {
                window.open(response.ssoAuthAddress, '_self');
            } else if (response?.mfa?.mfaTotp || response?.customAuth) {
                this._stateLogin.set({
                    ...this._stateLogin(),
                    isLoginMFA: true,
                    loading: true,
                });

                // Delay de 1s antes de navegar para MFA
                setTimeout(() => {
                    this._stateLogin.set({
                        ...this._stateLogin(),
                        loading: false,
                    });
                    this.router.navigate(['/conta/login-mfa']);
                }, 1000);
                return;
            }

            // Delay de 1s antes de navegar para senha
            setTimeout(() => {
                this._stateLogin.set({
                    ...this._stateLogin(),
                    loading: false,
                });
                this.router.navigate(['/conta/login-password']);
            }, 1000);
        } catch (error) {
            const msg = this.utils.getErrorMessage(error);
            errorCallback(msg);
        } finally {
            this.setLoading(false);
        }
    }

    async login(
        senha: string,
        errorCallback: (msg: string) => void = () => {}
    ) {
        try {
            this.setLoading(true);

            const authModel: IAuthenticateModel = {
                userNameOrEmailAddress: this._stateLogin().email,
                password: senha,
                loginAdminstrativo: this._stateLogin().isLoginAdmin,
                rememberClient: false,
                pin: this._stateLogin().pin || undefined,
            };

            const auth = await this.autenticacaoService.authenticate(authModel);

            if (auth) {
                this._accessTokens.set(auth.accessTokens);
                this.sessionStorage.setItem(
                    'accessTokens',
                    JSON.stringify(auth.accessTokens)
                );

                if (
                    auth.showMessagePasswordExpire &&
                    auth.daysPasswordExpire !== undefined
                ) {
                    alert(
                        `A sua senha irá expirar em ${auth.daysPasswordExpire} dias. Considere atualizá-la.`
                    );
                }

                await this.setCurrentLoginInformations();

                // Exibe modal de sucesso
                this.messageController.showSuccessModal(
                    'Usuário logado com sucesso!',
                    1500
                );

                // Delay de 1.5s antes de redirecionar (tempo do modal)
                setTimeout(() => {
                    this.setLoading(false);
                    this.router.navigate(['/plataforma']);
                }, 1500);
            }
        } catch (error) {
            const msg = this.utils.getErrorMessage(error);
            errorCallback(msg);
        } finally {
            this.setLoading(false);
        }
    }

    async isTenantAvailable(
        tenantName: string,
        errorCallback: (msg: string) => void = () => {}
    ): Promise<void> {
        try {
            this.setLoading(true);
            this.sessionStorage.removeItem('accessTokens');
            this.sessionStorage.removeItem('tenantId');

            const info = await this.autenticacaoService.isTenantAvailable(
                tenantName
            );
            switch (info?.state) {
                case EnumTenantAvailabilityState.Available:
                    if (info.tenantId !== undefined && info.tenantId !== null) {
                        this.sessionStorage.setItem(
                            'tenantId',
                            info.tenantId.toString()
                        );
                    } else {
                        errorCallback('TenantId não está disponível.');
                        return;
                    }
                    await this.setCurrentLoginInformations();
                    break;
                case EnumTenantAvailabilityState.InActive:
                    errorCallback('Tenant inativa');
                    break;
                case EnumTenantAvailabilityState.NotFound:
                    errorCallback('Tenant não existe');
                    break;
                default:
                    errorCallback('Erro desconhecido. Contate o suporte.');
            }
        } catch (error) {
            const msg = this.utils.getErrorMessage(error);
            errorCallback(msg);
        } finally {
            this.setLoading(false);
        }
    }

    async setCurrentLoginInformations(): Promise<void> {
        try {
            this.setLoading(true);
            const loginInfo =
                await this.autenticacaoService.getCurrentLoginInformations();
            const permissions =
                await this.autenticacaoService.getAllPermissions();

            if (loginInfo && permissions) {
                this._currentLoginInformations.set(loginInfo);
                this._permissions.set(permissions);
                this._authResolved.set(true);

                this.sessionStorage.setItem(
                    'currentLoginInformations',
                    JSON.stringify(loginInfo)
                );
                this.sessionStorage.setItem(
                    'permissions',
                    JSON.stringify(permissions)
                );
            } else {
                alert('Erro ao recuperar informações do login');
                this.redirectToLogin();
            }
        } catch (error) {
            alert(this.utils.getErrorMessage(error));
            this.redirectToLogin();
        } finally {
            this.setLoading(false);
        }
    }

    isGranted(permissionName: string): boolean {
        const granted = this._permissions()?.auth.grantedPermissions || {};
        return Object.entries(granted).some(
            ([key, value]) => key === permissionName && value
        );
    }

    logout(): void {
        this.setLoading(true);
        this.sessionStorage.removeItem('accessTokens');
        this.sessionStorage.removeItem('tenantId');
        this.sessionStorage.removeItem('currentLoginInformations');
        this.sessionStorage.removeItem('permissions');

        this._accessTokens.set([]);
        this._currentLoginInformations.set(null);
        this._permissions.set(null);
        this._authResolved.set(false);

        this.setLoading(false);
        this.redirectToLogin();
    }

    private redirectToLogin(): void {
        const route = this.router.url.includes('login-admin')
            ? '/conta/login-admin'
            : '/conta/login';
        this.router.navigate([route]);
    }

    private isLoginRoute(url: string): boolean {
        return ['login', 'login-admin', 'login-mfa'].some((route) =>
            url.includes(route)
        );
    }

    IsAbpResponse(obj: any): obj is AbpResponse<any> {
        return obj && typeof obj === 'object' && 'success' in obj;
    }

    setLoading(loading: boolean): void {
        this._stateLogin.set({
            ...this._stateLogin(),
            loading,
        });
    }

    setPin(pin: string): void {
        this._stateLogin.set({
            ...this._stateLogin(),
            pin,
            loading: true,
        });

        // Delay de 1s antes de navegar para senha
        setTimeout(() => {
            this._stateLogin.set({
                ...this._stateLogin(),
                loading: false,
            });
            this.router.navigate(['/conta/login-password']);
        }, 1000);
    }
}
