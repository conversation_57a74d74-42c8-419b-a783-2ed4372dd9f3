<div class="sign-in-area">
    <div class="d-table">
        <div class="d-table-cell">
            <div
                class="daxa-form ml-auto mr-auto border-radius bg-white position-relative"
                [class.card-borderd-theme]="
                    themeSettingsController.isCardBorder()
                "
                [class.component-dark-theme]="themeSettingsController.isDark()"
                [class.rtl-enabled]="themeSettingsController.isRTLEnabled()"
            >
                <!-- Loading overlay -->
                <app-loading
                    [isLoading]="autenticacaoController.isLoading()"
                    loadingText="Processando PIN..."
                ></app-loading>
                <div class="title text-center">
                    <img
                        src="/images/TS Logo.png"
                        alt="logo-icon"
                        class="inline-block w-10 mb-6! mx-auto"
                    />
                    <p class="text-center">
                        <span class="text-primary text-xl"
                            >Autenticação de Dois Fatores</span
                        >
                        <br />
                        Digite o código PIN de 4 dígitos para continuar.
                    </p>
                </div>

                <form [formGroup]="pinForm" class="pin-form">
                    <div class="pin-container">
                        <input
                            #pinInput
                            type="text"
                            maxlength="1"
                            class="pin-input"
                            formControlName="pin1"
                            (input)="onPinInput($event, 0)"
                            (keydown)="onPinKeydown($event, 0)"
                            (paste)="onPinPaste($event)"
                            autocomplete="off"
                            inputmode="numeric"
                            pattern="[0-9]*"
                        />
                        <input
                            #pinInput
                            type="text"
                            maxlength="1"
                            class="pin-input"
                            formControlName="pin2"
                            (input)="onPinInput($event, 1)"
                            (keydown)="onPinKeydown($event, 1)"
                            autocomplete="off"
                            inputmode="numeric"
                            pattern="[0-9]*"
                        />
                        <input
                            #pinInput
                            type="text"
                            maxlength="1"
                            class="pin-input"
                            formControlName="pin3"
                            (input)="onPinInput($event, 2)"
                            (keydown)="onPinKeydown($event, 2)"
                            autocomplete="off"
                            inputmode="numeric"
                            pattern="[0-9]*"
                        />
                        <input
                            #pinInput
                            type="text"
                            maxlength="1"
                            class="pin-input"
                            formControlName="pin4"
                            (input)="onPinInput($event, 3)"
                            (keydown)="onPinKeydown($event, 3)"
                            autocomplete="off"
                            inputmode="numeric"
                            pattern="[0-9]*"
                        />
                        <input
                            #pinInput
                            type="text"
                            maxlength="1"
                            class="pin-input"
                            formControlName="pin5"
                            (input)="onPinInput($event, 4)"
                            (keydown)="onPinKeydown($event, 4)"
                            autocomplete="off"
                            inputmode="numeric"
                            pattern="[0-9]*"
                        />
                        <input
                            #pinInput
                            type="text"
                            maxlength="1"
                            class="pin-input"
                            formControlName="pin6"
                            (input)="onPinInput($event, 5)"
                            (keydown)="onPinKeydown($event, 5)"
                            autocomplete="off"
                            inputmode="numeric"
                            pattern="[0-9]*"
                        />
                    </div>

                    <div class="pin-info">
                        <p class="text-center">
                            O formulário será enviado automaticamente quando
                            todos os dígitos forem preenchidos
                        </p>
                    </div>
                    <a
                        class="text-center cursor-pointer mt-10 w-100 block hover:underline!"
                        (click)="goBackToEmail()"
                    >
                        Entrar com outra conta
                    </a>
                </form>

                <div class="signin-with-socials text-center">
                    <div class="socials">
                        <button
                            mat-button
                            type="button"
                            class="twitter"
                            title="Visite nosso site institucional"
                            aria-label="Visite nosso site institucional"
                        >
                            <i class="ri-global-line"></i>
                        </button>
                        <button
                            mat-button
                            type="button"
                            class="google"
                            title="Visite nosso Instagram"
                            aria-label="Visite nosso Instagram"
                        >
                            <i class="ri-instagram-fill"></i>
                        </button>
                        <button
                            mat-button
                            type="button"
                            class="linkedin"
                            title="Visite nosso LinkedIn"
                            aria-label="Visite nosso LinkedIn"
                        >
                            <i class="ri-linkedin-fill"></i>
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
