import { Routes } from '@angular/router';
import { LoginComponent } from '@portal/auth/pages/login/login.component';
import { LoginPasswordComponent } from '@portal/auth/pages/login-password/login-password.component';
import { LoginMfaComponent } from '@portal/auth/pages/login-mfa/login-mfa.component';
import { NotFoundComponent } from '@shared/components/not-found/not-found.component';

export const authRoutes: Routes = [
    {
        path: 'conta',
        children: [
            { path: '', component: NotFoundComponent },
            { path: 'login', component: LoginComponent },
            { path: 'login-admin', component: LoginComponent },
            { path: 'login-password', component: LoginPasswordComponent },
            { path: 'login-mfa', component: LoginMfaComponent },
            // { path: 'login-mfa', component: SignMFAComponent, canActivate: [AppRouteGuard] },
        ],
    },
];
