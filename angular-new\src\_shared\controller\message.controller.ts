import { Injectable, signal } from '@angular/core';
import { ICustomModalConfig } from '@shared/components/toasts/custom-modal/custom-modal.component';

@Injectable({
    providedIn: 'root',
})
export class MessageController {
    private readonly _successModalOpen = signal<boolean>(false);
    private readonly _successMessage = signal<string>('');
    private readonly _errorModalOpen = signal<boolean>(false);
    private readonly _errorMessage = signal<string>('');
    private readonly _customModalOpen = signal<boolean>(false);
    private readonly _customModalConfig = signal<ICustomModalConfig>({
        title: 'Informação',
        message: 'Mensagem personalizada',
        icon: 'info',
        color: '#6c757d',
        autoCloseDelay: 3000,
    });

    readonly successModalOpen = this._successModalOpen.asReadonly();
    readonly successMessage = this._successMessage.asReadonly();
    readonly errorModalOpen = this._errorModalOpen.asReadonly();
    readonly errorMessage = this._errorMessage.asReadonly();
    readonly customModalOpen = this._customModalOpen.asReadonly();
    readonly customModalConfig = this._customModalConfig.asReadonly();

    showSuccess(message: string, useModal: boolean = false): void {
        if (useModal) {
            this._successMessage.set(message);
            this._successModalOpen.set(true);
        } else {
            alert(`✅ Sucesso: ${message}`);
        }
    }

    showError(message: string, useModal: boolean = false): void {
        if (useModal) {
            this._errorMessage.set(message);
            this._errorModalOpen.set(true);
        } else {
            alert(`❌ Erro: ${message}`);
        }
    }

    showInfo(message: string): void {
        alert(`ℹ️ Informação: ${message}`);
    }

    showWarning(message: string): void {
        alert(`⚠️ Alerta: ${message}`);
    }

    showSuccessModal(message: string, autoCloseDelay: number = 2500): void {
        this._successMessage.set(message);
        this._successModalOpen.set(true);

        setTimeout(() => {
            this.closeSuccessModal();
        }, autoCloseDelay);
    }

    closeSuccessModal(): void {
        this._successModalOpen.set(false);
        this._successMessage.set('');
    }

    showErrorModal(message: string, autoCloseDelay: number = 3000): void {
        this._errorMessage.set(message);
        this._errorModalOpen.set(true);

        setTimeout(() => {
            this.closeErrorModal();
        }, autoCloseDelay);
    }

    closeErrorModal(): void {
        this._errorModalOpen.set(false);
        this._errorMessage.set('');
    }

    /**
     * Exibe um modal customizável com configurações personalizadas
     * @param config Configuração do modal (título, mensagem, ícone, cor, etc.)
     */
    showCustomModal(config: Partial<ICustomModalConfig>): void {
        const fullConfig: ICustomModalConfig = {
            title: config.title || 'Informação',
            message: config.message || 'Mensagem personalizada',
            icon: config.icon || 'info',
            color: config.color || '#6c757d',
            autoCloseDelay: config.autoCloseDelay ?? 3000,
        };

        this._customModalConfig.set(fullConfig);
        this._customModalOpen.set(true);

        // Auto-close se especificado
        if (fullConfig.autoCloseDelay && fullConfig.autoCloseDelay > 0) {
            setTimeout(() => {
                this.closeCustomModal();
            }, fullConfig.autoCloseDelay);
        }
    }

    /**
     * Fecha o modal customizável
     */
    closeCustomModal(): void {
        this._customModalOpen.set(false);
    }

    /**
     * Métodos de conveniência para diferentes tipos de modal customizável
     */

    // Modal de informação (azul)
    showInfoModal(
        title: string,
        message: string,
        autoCloseDelay: number = 3000
    ): void {
        this.showCustomModal({
            title,
            message,
            icon: 'info',
            color: '#2196F3',
            autoCloseDelay,
        });
    }

    // Modal de aviso (laranja)
    showWarningModal(
        title: string,
        message: string,
        autoCloseDelay: number = 4000
    ): void {
        this.showCustomModal({
            title,
            message,
            icon: 'warning',
            color: '#ff9800',
            autoCloseDelay,
        });
    }

    // Modal genérico (cinza)
    showGenericModal(
        title: string,
        message: string,
        icon: string = 'help',
        autoCloseDelay: number = 3000
    ): void {
        this.showCustomModal({
            title,
            message,
            icon,
            color: '#6c757d',
            autoCloseDelay,
        });
    }
}
