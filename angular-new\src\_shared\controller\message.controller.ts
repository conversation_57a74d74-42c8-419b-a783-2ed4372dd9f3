import { Injectable, signal } from '@angular/core';

/**
 * Serviço provisório para exibição de mensagens
 * Será substituído por uma implementação mais robusta no futuro
 */
@Injectable({
    providedIn: 'root',
})
export class MessageController {
    private readonly _successModalOpen = signal<boolean>(false);
    private readonly _successMessage = signal<string>('');

    readonly successModalOpen = this._successModalOpen.asReadonly();
    readonly successMessage = this._successMessage.asReadonly();

    /**
     * Exibe uma mensagem de sucesso
     * @param message Mensagem a ser exibida
     * @param useModal Se deve usar o modal personalizado (padrão: false para manter compatibilidade)
     */
    showSuccess(message: string, useModal: boolean = false): void {
        if (useModal) {
            this._successMessage.set(message);
            this._successModalOpen.set(true);
        } else {
            alert(`✅ Sucesso: ${message}`);
        }
    }

    /**
     * Exibe o modal de sucesso com mensagem personalizada
     * @param message Mensagem a ser exibida
     * @param autoCloseDelay Tempo em ms para fechar automaticamente (padrão: 2000ms)
     */
    showSuccessModal(message: string, autoCloseDelay: number = 2000): void {
        this._successMessage.set(message);
        this._successModalOpen.set(true);

        // Auto-close após o delay especificado
        setTimeout(() => {
            this.closeSuccessModal();
        }, autoCloseDelay);
    }

    /**
     * Fecha o modal de sucesso
     */
    closeSuccessModal(): void {
        this._successModalOpen.set(false);
        this._successMessage.set('');
    }

    /**
     * Exibe uma mensagem de erro
     * @param message Mensagem a ser exibida
     */
    showError(message: string): void {
        alert(`❌ Erro: ${message}`);
    }

    /**
     * Exibe uma mensagem de informação
     * @param message Mensagem a ser exibida
     */
    showInfo(message: string): void {
        alert(`ℹ️ Informação: ${message}`);
    }

    /**
     * Exibe uma mensagem de alerta
     * @param message Mensagem a ser exibida
     */
    showWarning(message: string): void {
        alert(`⚠️ Alerta: ${message}`);
    }
}
