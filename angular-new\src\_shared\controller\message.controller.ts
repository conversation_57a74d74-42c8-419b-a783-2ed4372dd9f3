import { Injectable, signal } from '@angular/core';

@Injectable({
    providedIn: 'root',
})
export class MessageController {
    private readonly _successModalOpen = signal<boolean>(false);
    private readonly _successMessage = signal<string>('');
    private readonly _errorModalOpen = signal<boolean>(false);
    private readonly _errorMessage = signal<string>('');

    readonly successModalOpen = this._successModalOpen.asReadonly();
    readonly successMessage = this._successMessage.asReadonly();
    readonly errorModalOpen = this._errorModalOpen.asReadonly();
    readonly errorMessage = this._errorMessage.asReadonly();

    showSuccess(message: string, useModal: boolean = false): void {
        if (useModal) {
            this._successMessage.set(message);
            this._successModalOpen.set(true);
        } else {
            alert(`✅ Sucesso: ${message}`);
        }
    }

    showError(message: string, useModal: boolean = false): void {
        if (useModal) {
            this._errorMessage.set(message);
            this._errorModalOpen.set(true);
        } else {
            alert(`❌ Erro: ${message}`);
        }
    }

    showInfo(message: string): void {
        alert(`ℹ️ Informação: ${message}`);
    }

    showWarning(message: string): void {
        alert(`⚠️ Alerta: ${message}`);
    }

    showSuccessModal(message: string, autoCloseDelay: number = 2500): void {
        this._successMessage.set(message);
        this._successModalOpen.set(true);

        setTimeout(() => {
            this.closeSuccessModal();
        }, autoCloseDelay);
    }

    closeSuccessModal(): void {
        this._successModalOpen.set(false);
        this._successMessage.set('');
    }

    showErrorModal(message: string, autoCloseDelay: number = 3000): void {
        this._errorMessage.set(message);
        this._errorModalOpen.set(true);

        setTimeout(() => {
            this.closeErrorModal();
        }, autoCloseDelay);
    }

    closeErrorModal(): void {
        this._errorModalOpen.set(false);
        this._errorMessage.set('');
    }
}
