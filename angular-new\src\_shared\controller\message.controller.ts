import { Injectable, signal } from '@angular/core';

/**
 * Serviço provisório para exibição de mensagens
 * Será substituído por uma implementação mais robusta no futuro
 */
@Injectable({
    providedIn: 'root',
})
export class MessageController {
    private readonly _successModalOpen = signal<boolean>(true);
    private readonly _successMessage = signal<string>('');

    readonly successModalOpen = this._successModalOpen.asReadonly();
    readonly successMessage = this._successMessage.asReadonly();

     * Exibe uma mensagem de sucesso
     * @param message Mensagem a ser exibida
     * @param useModal Se deve usar o modal personalizado (padrão: false para manter compatibilidade)
     */
    showSuccess(message: string, useModal: boolean = false): void {
        if (useModal) {
            this._successMessage.set(message);
            this._successModalOpen.set(true);
        } else {
            alert(`✅ Sucesso: ${message}`);
        }
    }

    showSuccessModal(message: string, autoCloseDelay: number = 2500): void {
        this._successMessage.set(message);
        this._successModalOpen.set(true);

        setTimeout(() => {
            this.closeSuccessModal();
        }, autoCloseDelay);
    }

    closeSuccessModal(): void {
        this._successModalOpen.set(false);
        this._successMessage.set('');
    }

    showError(message: string): void {
        alert(`❌ Erro: ${message}`);
    }

    showInfo(message: string): void {
        alert(`ℹ️ Informação: ${message}`);
    }

    showWarning(message: string): void {
        alert(`⚠️ Alerta: ${message}`);
    }
}
