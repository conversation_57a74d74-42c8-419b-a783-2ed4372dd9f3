import {
    Component,
    Input,
    Output,
    EventEmitter,
    signal,
    Signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-error-modal',
    imports: [CommonModule],
    templateUrl: './error-modal.component.html',
    styleUrl: './error-modal.component.scss',
    standalone: true,
})
export class ErrorModalComponent {
    @Input() isOpen: Signal<boolean> = signal<boolean>(false);
    @Input() message: string = 'Ocorreu um erro inesperado!';
    @Input() autoCloseDelay: number = 3000; // 3 segundos por padrão
    @Output() close = new EventEmitter<void>();

    private autoCloseTimeout?: number;

    ngOnChanges(): void {
        if (this.isOpen()) {
            this.startAutoClose();
        } else {
            this.clearAutoClose();
        }
    }

    ngOnDestroy(): void {
        this.clearAutoClose();
    }

    private startAutoClose(): void {
        this.clearAutoClose();
        this.autoCloseTimeout = window.setTimeout(() => {
            this.onClose();
        }, this.autoCloseDelay);
    }

    private clearAutoClose(): void {
        if (this.autoCloseTimeout) {
            clearTimeout(this.autoCloseTimeout);
            this.autoCloseTimeout = undefined;
        }
    }

    onClose(): void {
        this.clearAutoClose();
        this.close.emit();
    }

    onOverlayClick(event: Event): void {
        if (event.target === event.currentTarget) {
            this.onClose();
        }
    }
}
