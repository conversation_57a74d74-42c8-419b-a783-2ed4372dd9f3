<app-generic-form-modal
    [isOpen]="modalChangeTenant"
    [title]="'Informar identificador'"
    [fields]="formFields"
    [submitButtonText]="'Salvar'"
    (formSubmit)="onFormSubmit($event)"
    (formCancel)="onFormCancel()"
></app-generic-form-modal>

<div class="tenant-change-component py-4">
    <span>
        <span class="pr-1.5">Identificador:</span>
        <span *ngIf="tenant" title="{{ tenant.name }}">
            <strong>{{ tenant.tenancyName }}</strong>
        </span>
        <span *ngIf="!tenant">Não selecionado</span>
        (<span class="ts-link-style" (click)="modalChangeTenant.set(true)">
            {{ tenant ? "Alterar" : "Selecionar" }} </span
        >)
    </span>
</div>
