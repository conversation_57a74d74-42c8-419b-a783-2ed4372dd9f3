<div
    [ngClass]="{
        'blank-page': isBlankPageRoute(router.url)
    }"
    [class.card-borderd-theme]="themeSettingsController.isCardBorder()"
    [class.card-border-radius]="themeSettingsController.isCardBorderRadius()"
    [class.rtl-enabled]="themeSettingsController.isRTLEnabled()"
>
    <!-- Sidebar  -->
    <app-sidebar />

    <!-- Main Content -->
    <div
        class="main-content transition d-flex flex-column"
        [ngClass]="{
            active: isSidebarToggled
        }"
        [class.right-sidebar]="themeSettingsController.isRightSidebar()"
        [class.hide-sidebar]="themeSettingsController.isHideSidebar()"
    >
        <app-header />
        <router-outlet />
        <div class="flex-grow-1"></div>
        <app-footer />
    </div>

    <!-- Customizer Settings -->
    <!-- <app-customizer-settings /> -->
</div>

<!-- Modal de Sucesso Global -->
<app-success-modal
    [isOpen]="messageController.successModalOpen"
    [message]="messageController.successMessage()"
    (close)="messageController.closeSuccessModal()"
></app-success-modal>

<!-- Modal de Erro Global -->
<app-error-modal
    [isOpen]="messageController.errorModalOpen"
    [message]="messageController.errorMessage()"
    (close)="messageController.closeErrorModal()"
></app-error-modal>

<!-- Modal Customizável Global -->
<app-custom-modal
    [isOpen]="messageController.customModalOpen"
    [config]="messageController.customModalConfig()"
    (close)="messageController.closeCustomModal()"
></app-custom-modal>
