import { HttpClient, HttpHeaders, HttpParams } from '@angular/common/http';
import { inject, Injectable } from '@angular/core';
import { AutenticacaoUtils } from '../utils/autenticacao.utils';
import { firstValueFrom, map, Observable } from 'rxjs';
import {
    AuthenticateResultModelDto,
    IAuthenticateModel,
    IGetCurrentLoginInformationsOutput,
    IMfaInformationDto,
    IPermissionListResultDto,
    ITenantAvailableOutput,
} from '../entities/autenticacao';
import { Environment } from '@environments/environment';

@Injectable({
    providedIn: 'root',
})
export class AutenticacaoService {
    private http = inject(HttpClient);
    private _autenticacaoUtils = inject(AutenticacaoUtils);

    async GetLoginInformation(
        email: string,
        loginAdmin: boolean
    ): Promise<{ mfa: IMfaInformationDto; customAuth: boolean } | null> {
        const emailCriptografado = encodeURIComponent(
            this._autenticacaoUtils.criptografarValor(email)
        );

        const url_ = `${Environment.remoteServicePortalUrl}/api/TokenAuth/GetCustomLoginInformation`;
        const params = new HttpParams()
            .set('EmailAddress', emailCriptografado)
            .set('LoginAdminstrativo', loginAdmin.toString());

        return await firstValueFrom(
            this.http.get<{ mfa: IMfaInformationDto; customAuth: boolean }>(
                url_,
                {
                    params,
                }
            )
        );
    }

    async authenticate(
        authenticateModel: IAuthenticateModel
    ): Promise<AuthenticateResultModelDto> {
        let url_ = `${Environment.remoteServicePortalUrl}/api/TokenAuth/Authenticate`;
        url_ = url_.replace(/[?&]$/, '');

        const body = this.criptografarDadosLogin(authenticateModel);
        const headers = new HttpHeaders({
            'Content-Type': 'application/json-patch+json',
            Accept: 'text/plain',
        });

        return await firstValueFrom(
            this.http.post<AuthenticateResultModelDto>(url_, body, {
                headers,
            })
        );
    }

    criptografarDadosLogin(dadosLogin: IAuthenticateModel) {
        dadosLogin.password = this._autenticacaoUtils.criptografarValor(
            dadosLogin.password
        );
        dadosLogin.userNameOrEmailAddress =
            this._autenticacaoUtils.criptografarValor(
                dadosLogin.userNameOrEmailAddress
            );
        if (dadosLogin.pin)
            dadosLogin.pin = this._autenticacaoUtils.criptografarValor(
                dadosLogin.pin
            );
        return JSON.stringify(dadosLogin);
    }

    async isTenantAvailable(
        tenancyName: string
    ): Promise<ITenantAvailableOutput> {
        let url_ = `${Environment.remoteServicePortalUrl}/api/services/app/Account/IsTenantAvailable`;

        const body = JSON.stringify({ tenancyName });
        const headers = new HttpHeaders({
            'Content-Type': 'application/json-patch+json',
            Accept: 'text/plain',
        });

        return await firstValueFrom(
            this.http.post<ITenantAvailableOutput>(url_, body, {
                headers,
            })
        );
    }

    async getCurrentLoginInformations(): Promise<IGetCurrentLoginInformationsOutput> {
        let url_ = `${Environment.remoteServicePortalUrl}/api/services/app/Session/GetCurrentLoginInformations`;

        return await firstValueFrom(
            this.http.get<IGetCurrentLoginInformationsOutput>(url_)
        );
    }

    async getAllPermissions(): Promise<IPermissionListResultDto> {
        let url_ = `${Environment.remoteServicePortalUrl}/api/AbpUserConfiguration/GetAll`;
        url_ = url_.replace(/[?&]$/, '');

        return await firstValueFrom(
            this.http.get<IPermissionListResultDto>(url_)
        );
    }
}
