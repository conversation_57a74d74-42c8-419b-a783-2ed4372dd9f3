<form [formGroup]="form" (ngSubmit)="onSubmit()">
    <div class="form-fields">
        @for (field of fields; track field.name) {
        <div
            class="form-field"
            [ngClass]="getFieldColClass(field)"
            [ngStyle]="{ width: getFieldWidth(field) }"
        >
            @switch (field.type) { @case ('text') {
            <mat-form-field>
                <mat-label>{{ field.label }}</mat-label>
                <input
                    matInput
                    [formControlName]="field.name"
                    [placeholder]="field.placeholder || ''"
                />
                @if (field.hint) {
                <mat-hint>{{ field.hint }}</mat-hint>
                } @if (form.get(field.name)?.hasError('required') &&
                form.get(field.name)?.touched) {
                <mat-error>Este campo é obrigatório</mat-error>
                } @if (form.get(field.name)?.hasError('email') &&
                form.get(field.name)?.touched) {
                <mat-error>Email inválido</mat-error>
                } @if (form.get(field.name)?.hasError('min') &&
                form.get(field.name)?.touched) {
                <mat-error>Valor mínimo: {{ field.rules?.min }}</mat-error>
                } @if (form.get(field.name)?.hasError('max') &&
                form.get(field.name)?.touched) {
                <mat-error>Valor máximo: {{ field.rules?.max }}</mat-error>
                } @if (form.get(field.name)?.hasError('minlength') &&
                form.get(field.name)?.touched) {
                <mat-error
                    >Mínimo de
                    {{ field.rules?.minLength }} caracteres</mat-error
                >
                } @if (form.get(field.name)?.hasError('maxlength') &&
                form.get(field.name)?.touched) {
                <mat-error
                    >Máximo de
                    {{ field.rules?.maxLength }} caracteres</mat-error
                >
                } @if (form.get(field.name)?.hasError('pattern') &&
                form.get(field.name)?.touched) {
                <mat-error>Formato inválido</mat-error>
                }
            </mat-form-field>
            } @case ('number') {
            <mat-form-field>
                <mat-label>{{ field.label }}</mat-label>
                <input
                    matInput
                    type="number"
                    [formControlName]="field.name"
                    [placeholder]="field.placeholder || ''"
                />
                @if (form.get(field.name)?.hasError('required') &&
                form.get(field.name)?.touched) {
                <mat-error>Este campo é obrigatório</mat-error>
                } @if (form.get(field.name)?.hasError('min') &&
                form.get(field.name)?.touched) {
                <mat-error>Valor mínimo: {{ field.rules?.min }}</mat-error>
                } @if (form.get(field.name)?.hasError('max') &&
                form.get(field.name)?.touched) {
                <mat-error>Valor máximo: {{ field.rules?.max }}</mat-error>
                }
            </mat-form-field>
            } @case ('email') {
            <mat-form-field>
                <mat-label>{{ field.label }}</mat-label>
                <input
                    matInput
                    type="email"
                    [formControlName]="field.name"
                    [placeholder]="field.placeholder || ''"
                />
                @if (form.get(field.name)?.hasError('required') &&
                form.get(field.name)?.touched) {
                <mat-error>Este campo é obrigatório</mat-error>
                } @if (form.get(field.name)?.hasError('email') &&
                form.get(field.name)?.touched) {
                <mat-error>Email inválido</mat-error>
                }
            </mat-form-field>
            } @case ('password') {
            <mat-form-field>
                <mat-label>{{ field.label }}</mat-label>
                <input
                    matInput
                    type="password"
                    [formControlName]="field.name"
                    [placeholder]="field.placeholder || ''"
                />
                @if (field.hint) {
                <mat-hint>{{ field.hint }}</mat-hint>
                } @if (form.get(field.name)?.hasError('required') &&
                form.get(field.name)?.touched) {
                <mat-error>Este campo é obrigatório</mat-error>
                }
            </mat-form-field>
            } @case ('select') {
            <mat-form-field>
                <mat-label>{{ field.label }}</mat-label>
                <mat-select [formControlName]="field.name">
                    @for (option of field.options; track option.value) {
                    <mat-option [value]="option.value">{{
                        option.label
                    }}</mat-option>
                    }
                </mat-select>
                @if (field.hint) {
                <mat-hint>{{ field.hint }}</mat-hint>
                } @if (form.get(field.name)?.hasError('required') &&
                form.get(field.name)?.touched) {
                <mat-error>Este campo é obrigatório</mat-error>
                }
            </mat-form-field>
            } @case ('multiselect') {
            <mat-form-field>
                <mat-label>{{ field.label }}</mat-label>
                <mat-select [formControlName]="field.name" multiple>
                    @for (option of field.options; track option.value) {
                    <mat-option [value]="option.value">{{
                        option.label
                    }}</mat-option>
                    }
                </mat-select>
                <mat-hint>
                    {{ field.hint || "" }}
                </mat-hint>
                @if (form.get(field.name)?.hasError('required') &&
                form.get(field.name)?.touched) {
                <mat-error>Este campo é obrigatório</mat-error>
                }
            </mat-form-field>
            } @case ('date') {
            <mat-form-field>
                <mat-label>{{ field.label }}</mat-label>
                <input
                    matInput
                    [matDatepicker]="picker"
                    [formControlName]="field.name"
                    [placeholder]="field.placeholder || ''"
                />
                <mat-datepicker-toggle
                    matIconSuffix
                    [for]="picker"
                ></mat-datepicker-toggle>
                <mat-datepicker #picker></mat-datepicker>
                @if (form.get(field.name)?.hasError('required') &&
                form.get(field.name)?.touched) {
                <mat-error>Este campo é obrigatório</mat-error>
                }
            </mat-form-field>
            } @case ('checkbox') {
            <div class="checkbox-field">
                <mat-checkbox [formControlName]="field.name">{{
                    field.label
                }}</mat-checkbox>
            </div>
            } @case ('textarea') {
            <mat-form-field>
                <mat-label>{{ field.label }}</mat-label>
                <textarea
                    matInput
                    [formControlName]="field.name"
                    [placeholder]="field.placeholder || ''"
                    rows="3"
                ></textarea>
                @if (field.hint) {
                <mat-hint>{{ field.hint }}</mat-hint>
                } @if (form.get(field.name)?.hasError('required') &&
                form.get(field.name)?.touched) {
                <mat-error>Este campo é obrigatório</mat-error>
                } @if (form.get(field.name)?.hasError('minlength') &&
                form.get(field.name)?.touched) {
                <mat-error
                    >Mínimo de
                    {{ field.rules?.minLength }} caracteres</mat-error
                >
                } @if (form.get(field.name)?.hasError('maxlength') &&
                form.get(field.name)?.touched) {
                <mat-error
                    >Máximo de
                    {{ field.rules?.maxLength }} caracteres</mat-error
                >
                }
            </mat-form-field>
            } @default {
            <mat-form-field>
                <mat-label>{{ field.label }}</mat-label>
                <input
                    matInput
                    [formControlName]="field.name"
                    [placeholder]="field.placeholder || ''"
                />
            </mat-form-field>
            } }
        </div>
        }
    </div>

    <div class="form-actions">
        @if (!hideCancelButton) {
        <button type="button" mat-button (click)="onCancel()">
            {{ cancelButtonText }}
        </button>
        } @if (resetButtonText) {
        <button type="button" mat-button (click)="form.reset()">
            {{ resetButtonText }}
        </button>
        }
        <button
            type="submit"
            mat-flat-button
            color="primary"
            [ngClass]="{ block: submitButtonBlock }"
        >
            {{ submitButtonText }}
        </button>
    </div>
</form>
