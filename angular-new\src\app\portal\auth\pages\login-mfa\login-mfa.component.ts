import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { Router } from '@angular/router';
import { AutenticacaoController } from '../../controller/autenticacao.controller';
import { ThemeSettingsController } from '@app/portal/layout/controller/themeSettings.controller';
import { MatButtonModule } from '@angular/material/button';
import { MessageController } from '@shared/controller/message.controller';

@Component({
    selector: 'app-login-mfa',
    imports: [CommonModule, MatCardModule, MatButtonModule],
    templateUrl: './login-mfa.component.html',
    styleUrl: './login-mfa.component.scss',
})
export class LoginMfaComponent {
    themeSettingsController = inject(ThemeSettingsController);
    autenticacaoController = inject(AutenticacaoController);
    messageController = inject(MessageController);

    router = inject(Router);

    constructor() {}

    handleFormSubmit(formData: any): void {
        this.autenticacaoController.setPin(formData.pin);
    }
}
