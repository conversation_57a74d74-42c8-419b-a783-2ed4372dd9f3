import { CommonModule } from '@angular/common';
import {
    Component,
    inject,
    ElementRef,
    ViewChildren,
    QueryList,
    OnInit,
    AfterViewInit,
} from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { Router } from '@angular/router';
import { AutenticacaoController } from '../../controller/autenticacao.controller';
import { ThemeSettingsController } from '@app/portal/layout/controller/themeSettings.controller';
import { MatButtonModule } from '@angular/material/button';
import { MessageController } from '@shared/controller/message.controller';
import {
    FormBuilder,
    FormGroup,
    Validators,
    ReactiveFormsModule,
} from '@angular/forms';
import { MatInputModule } from '@angular/material/input';
import { MatFormFieldModule } from '@angular/material/form-field';
import { LoadingComponent } from '@shared/components/generic/loading/loading.component';

@Component({
    selector: 'app-login-mfa',
    imports: [
        CommonModule,
        MatCardModule,
        MatButtonModule,
        ReactiveFormsModule,
        MatInputModule,
        MatFormFieldModule,
        LoadingComponent,
    ],
    templateUrl: './login-mfa.component.html',
    styleUrl: './login-mfa.component.scss',
})
export class LoginMfaComponent implements AfterViewInit, OnInit {
    @ViewChildren('pinInput') pinInputs!: QueryList<ElementRef>;

    themeSettingsController = inject(ThemeSettingsController);
    autenticacaoController = inject(AutenticacaoController);
    messageController = inject(MessageController);
    router = inject(Router);
    formBuilder = inject(FormBuilder);

    pinForm!: FormGroup;

    ngOnInit(): void {
        this.pinForm = this.formBuilder.group({
            pin1: ['', [Validators.required, Validators.pattern(/^\d$/)]],
            pin2: ['', [Validators.required, Validators.pattern(/^\d$/)]],
            pin3: ['', [Validators.required, Validators.pattern(/^\d$/)]],
            pin4: ['', [Validators.required, Validators.pattern(/^\d$/)]],
            pin5: ['', [Validators.required, Validators.pattern(/^\d$/)]],
            pin6: ['', [Validators.required, Validators.pattern(/^\d$/)]],
        });

        if (!this.autenticacaoController.stateLogin().validEmail) {
            this.router.navigate(['/conta/login']);
        }
    }

    ngAfterViewInit(): void {
        setTimeout(() => {
            if (this.pinInputs.first) {
                this.pinInputs.first.nativeElement.focus();
            }
        });
    }

    onPinInput(event: any, index: number): void {
        const input = event.target;
        const value = input.value;

        if (!/^\d$/.test(value) && value !== '') {
            input.value = '';
            return;
        }

        if (value && index < 6) {
            const nextInput = this.pinInputs.toArray()[index + 1];
            if (nextInput) nextInput.nativeElement.focus();
        }

        this.checkAndSubmitPin();
    }

    onPinKeydown(event: KeyboardEvent, index: number): void {
        const allowedKeys = [
            'Backspace',
            'Delete',
            'Tab',
            'ArrowLeft',
            'ArrowRight',
        ];
        const isNumber = /^\d$/.test(event.key);

        if (!isNumber && !allowedKeys.includes(event.key)) {
            event.preventDefault();
            return;
        }

        if (
            event.key === 'Backspace' &&
            !(event.currentTarget as HTMLInputElement)?.value &&
            index > 0
        ) {
            const prevInput = this.pinInputs.toArray()[index - 1];
            if (prevInput) prevInput.nativeElement.focus();
        }
    }

    onPinPaste(event: ClipboardEvent): void {
        event.preventDefault();
        const pastedData = event.clipboardData?.getData('text') || '';
        const digits = pastedData.replace(/\D/g, '').slice(0, 4);

        if (digits.length === 4) {
            const inputs = this.pinInputs.toArray();
            digits.split('').forEach((digit, index) => {
                if (inputs[index]) {
                    inputs[index].nativeElement.value = digit;
                    this.pinForm.get(`pin${index + 1}`)?.setValue(digit);
                }
            });
            this.checkAndSubmitPin();
        }
    }

    private checkAndSubmitPin(): void {
        const inputs = this.pinInputs.toArray();
        inputs.forEach((input, index) => {
            this.pinForm
                .get(`pin${index + 1}`)
                ?.setValue(input.nativeElement.value);
        });

        if (this.pinForm.valid) {
            const pin = Object.values(this.pinForm.value).join('');
            try {
                this.autenticacaoController.setPin(pin);
            } catch (error: any) {
                // Para o fluxo de login, usar modal de erro
                this.messageController.showError(
                    error.message || 'Erro ao processar PIN',
                    true
                );
                this.clearPin();
            }
        }
    }

    private clearPin(): void {
        this.pinForm.reset();
        const inputs = this.pinInputs.toArray();
        inputs.forEach((input) => {
            input.nativeElement.value = '';
        });
        if (inputs[0]) {
            inputs[0].nativeElement.focus();
        }
    }

    handleFormSubmit(formData: any): void {
        this.autenticacaoController.setPin(formData.pin);
    }
}
