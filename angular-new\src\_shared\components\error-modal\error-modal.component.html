<div class="error-modal-overlay" *ngIf="isOpen()" (click)="onOverlayClick($event)">
    <div class="error-modal-container">
        <div class="error-modal-content">
            <div class="error-icon">
                <div class="error-mark">
                    <div class="error-circle">
                        <div class="error-line error-line-left"></div>
                        <div class="error-line error-line-right"></div>
                    </div>
                </div>
            </div>
            <div class="error-message">
                <h3>Erro!</h3>
                <p>{{ message }}</p>
            </div>
        </div>
    </div>
</div>
