import { CommonModule } from '@angular/common';
import { Component, inject, OnInit } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { Router } from '@angular/router';
import { GenericFormComponent, IFormField } from '@shared/components/generic';
import { AutenticacaoController } from '../../controller/autenticacao.controller';
import { ThemeSettingsController } from '@app/portal/layout/controller/themeSettings.controller';
import { MatButtonModule } from '@angular/material/button';
import { MessageController } from '@shared/controller/message.controller';
import { LoadingComponent } from '@shared/components/generic/loading/loading.component';

@Component({
    selector: 'app-login-password',
    imports: [
        CommonModule,
        MatCardModule,
        GenericFormComponent,
        MatButtonModule,
        LoadingComponent,
    ],
    templateUrl: './login-password.component.html',
    styleUrl: './login-password.component.scss',
})
export class LoginPasswordComponent implements OnInit {
    themeSettingsController = inject(ThemeSettingsController);
    autenticacaoController = inject(AutenticacaoController);
    messageController = inject(MessageController);

    router = inject(Router);

    formFields: IFormField[] = [
        {
            name: 'password',
            label: 'Senha',
            type: 'password',
            required: true,
            placeholder: 'Digite sua senha',
            col: 12,
        },
    ];

    constructor() {}

    ngOnInit(): void {
        if (!this.autenticacaoController.stateLogin().validEmail) {
            this.router.navigate(['/conta/login']);
        }
    }

    handleFormSubmit(formData: any): void {
        this.autenticacaoController.login(formData.password, (mensagemErro) =>
            this.errorLogin(mensagemErro)
        );
    }

    errorLogin(mensagem: string) {
        this.messageController.showError(mensagem, true);
    }
}
