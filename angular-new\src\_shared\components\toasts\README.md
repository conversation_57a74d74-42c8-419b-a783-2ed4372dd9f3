# 🎉 Sistema de Modais de Toast

Este diretório contém os componentes de modal para exibição de mensagens ao usuário.

## 📁 Estrutura

```
toasts/
├── success-modal/     # Modal de sucesso (verde)
├── error-modal/       # Modal de erro (vermelho)
├── custom-modal/      # Modal customizável (qualquer cor/ícone)
└── README.md         # Este arquivo
```

## 🚀 Como Usar

### 1. Modal de Sucesso
```typescript
// Método simples
this.messageController.showSuccessModal('Operação realizada com sucesso!');

// Com delay personalizado
this.messageController.showSuccessModal('Salvo!', 1500);
```

### 2. Modal de Erro
```typescript
// Método simples
this.messageController.showErrorModal('Erro ao processar dados');

// Com delay personalizado
this.messageController.showErrorModal('Falha na conexão', 4000);
```

### 3. Modal Customizável

#### Métodos de Conveniência:
```typescript
// Modal de informação (azul)
this.messageController.showInfoModal(
    'Informação Importante', 
    'Esta é uma mensagem informativa'
);

// Modal de aviso (laranja)
this.messageController.showWarningModal(
    'Atenção', 
    'Verifique os dados antes de continuar'
);

// Modal genérico (cinza)
this.messageController.showGenericModal(
    'Situação Adversa', 
    'Algo inesperado aconteceu',
    'help' // ícone
);
```

#### Método Completo:
```typescript
this.messageController.showCustomModal({
    title: 'Título Personalizado',
    message: 'Mensagem detalhada aqui',
    icon: 'school', // Ícone do Material Icons
    color: '#9c27b0', // Cor personalizada (roxo)
    autoCloseDelay: 5000 // 5 segundos
});
```

## 🎨 Ícones Disponíveis

Qualquer ícone do Material Symbols pode ser usado:

### Comuns:
- `check` - Sucesso
- `close` - Erro
- `info` - Informação
- `warning` - Aviso
- `help` - Ajuda
- `school` - Educação/Aprendizado
- `lightbulb` - Ideia
- `notifications` - Notificação
- `security` - Segurança
- `settings` - Configurações

### Situações Adversas:
- `help` - Dúvida/Ajuda
- `psychology` - Reflexão
- `lightbulb` - Sugestão
- `info` - Informação neutra
- `priority_high` - Prioridade
- `report_problem` - Problema reportado

## 🎨 Cores Sugeridas

### Pré-definidas:
- **Sucesso**: `#4CAF50` (verde)
- **Erro**: `#f44336` (vermelho)
- **Informação**: `#2196F3` (azul)
- **Aviso**: `#ff9800` (laranja)
- **Genérico**: `#6c757d` (cinza)

### Personalizadas:
- **Roxo**: `#9c27b0`
- **Rosa**: `#e91e63`
- **Índigo**: `#3f51b5`
- **Teal**: `#009688`
- **Marrom**: `#795548`

## 📱 Responsividade

Todos os modais são responsivos e se adaptam automaticamente a:
- **Desktop**: Tamanho completo com animações
- **Tablet**: Tamanho médio
- **Mobile**: Tamanho compacto com margens

## ⚙️ Configurações

### Auto-close:
- **Sucesso**: 2 segundos (padrão)
- **Erro**: 3 segundos (padrão)
- **Customizável**: 3 segundos (padrão)
- **Desabilitar**: `autoCloseDelay: 0`

### Fechamento:
- Clique no overlay
- Auto-close (se configurado)
- Programaticamente: `messageController.closeCustomModal()`

## 🔧 Exemplos Práticos

### Fluxo de Login:
```typescript
// Sucesso no login
this.messageController.showSuccessModal('Usuário logado com sucesso!', 1500);

// Erro de credenciais
this.messageController.showErrorModal('Email ou senha incorretos');

// Problema com tenant
this.messageController.showWarningModal(
    'Problema com Tenant',
    'Verifique se o tenant está ativo'
);
```

### Situações Adversas:
```typescript
// Manutenção programada
this.messageController.showCustomModal({
    title: 'Manutenção Programada',
    message: 'O sistema estará em manutenção das 02:00 às 04:00',
    icon: 'schedule',
    color: '#6c757d',
    autoCloseDelay: 0 // Não fecha automaticamente
});

// Funcionalidade em desenvolvimento
this.messageController.showGenericModal(
    'Em Desenvolvimento',
    'Esta funcionalidade estará disponível em breve',
    'construction'
);
```

## 🎯 Boas Práticas

1. **Use cores apropriadas** para o contexto
2. **Mantenha mensagens concisas** e claras
3. **Escolha ícones intuitivos** que representem a situação
4. **Configure delays apropriados** (sucesso: rápido, erro: mais tempo)
5. **Para situações críticas**, desabilite o auto-close
6. **Use o modal genérico** para situações que não se encaixam em sucesso/erro
