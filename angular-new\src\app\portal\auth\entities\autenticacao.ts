import moment from 'moment';
import {
    EnumTenantAvailabilityState,
    EnumTipoContrato,
    EnumTipoPlano,
} from './autenticacao.enum';
import { EnumModulo } from '@shared/entities/enums/modulo.enum';
//Interfaces
export interface IAuthenticateModel {
    userNameOrEmailAddress: string;
    password: string;
    rememberClient: boolean | undefined;
    pin: string | undefined;
    loginAdminstrativo: boolean;
}

export interface IAuthState {
    email: string;
    pin: string | undefined;
    tenantId: number | undefined;
    isLoginAdmin: boolean;
    isLoginMFA: boolean;
    loading: boolean;
}

export interface IMfaInformationDto {
    messageMfa: string;
    mfaTotp: boolean | undefined;
    manualEntryKey: string;
    qrCodeSetupImageUrl: string;
}

export interface AuthenticateResultModelDto {
    accessTokens: ModuleAccessToken[];
    expireInSeconds: number;
    userId: number;
    daysPasswordExpire: number;
    showMessagePasswordExpire: boolean;
    error: BackendDefaultError;
}

export interface ModuleAccessToken {
    accessToken: string;
    encryptedAccessToken: string;
    modulo: EnumModulo | null;
}

export interface BackendDefaultError {
    code: number;
    message: string;
}

export interface ITenantAvailableOutput {
    state: EnumTenantAvailabilityState;
    tenantId: number | undefined;
}

export interface IGetCurrentLoginInformationsOutput {
    application: IApplicationInfoDto;
    user: IUserLoginInfoDto | null;
    tenant: ITenantLoginInfoDto | null;
    modulos: ITenantModule[];
}

export interface IApplicationInfoDto {
    version: string | undefined;
    releaseDate: moment.Moment;
    features: { [key: string]: boolean } | undefined;
}

export interface IUserLoginInfoDto {
    name: string | undefined;
    surname: string | undefined;
    userName: string | undefined;
    emailAddress: string | undefined;
    id: number;
}

export interface ITenantLoginInfoDto {
    tenancyName: string | undefined;
    name: string | undefined;
    id: number;
    tenantPermissoes: ITenantPermissoes;
}

export interface ITenantPermissoes {
    tipoPlano: EnumTipoPlano;
    tipoContrato: EnumTipoContrato;
    funcinalidades: string[];
}

export interface ITenantModule {
    modulo: number;
    applicationId: number;
    tenantIdPortal: number;
    bancoDadosProduto: string;
    bancoDadosLog: string;
    ativo: boolean;
    editavel: boolean;
    alterado: boolean;
}

export interface IPermissionListResultDto {
    auth: IAuthDto;
}

export interface IAuthDto {
    allPermissions: PermissionsMap;
    grantedPermissions: PermissionsMap;
}

export interface PermissionsMap {
    [key: string]: true | false;
}
