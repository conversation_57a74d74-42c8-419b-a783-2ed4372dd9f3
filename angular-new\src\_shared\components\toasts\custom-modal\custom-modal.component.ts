import {
    Component,
    Input,
    Output,
    EventEmitter,
    signal,
    Signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';

export interface ICustomModalConfig {
    title: string;
    message: string;
    icon: string;
    color: string;
    autoCloseDelay?: number;
}

@Component({
    selector: 'app-custom-modal',
    imports: [CommonModule],
    templateUrl: './custom-modal.component.html',
    styleUrl: './custom-modal.component.scss',
    standalone: true,
})
export class CustomModalComponent {
    @Input() isOpen: Signal<boolean> = signal<boolean>(false);
    @Input() config: ICustomModalConfig = {
        title: 'Informação',
        message: 'Mensagem personalizada',
        icon: 'info',
        color: '#6c757d', // Cinza por padrão
        autoCloseDelay: 3000
    };
    @Output() close = new EventEmitter<void>();

    private autoCloseTimeout?: number;

    ngOnChanges(): void {
        if (this.isOpen()) {
            this.startAutoClose();
        } else {
            this.clearAutoClose();
        }
    }

    ngOnDestroy(): void {
        this.clearAutoClose();
    }

    private startAutoClose(): void {
        this.clearAutoClose();
        if (this.config.autoCloseDelay && this.config.autoCloseDelay > 0) {
            this.autoCloseTimeout = window.setTimeout(() => {
                this.onClose();
            }, this.config.autoCloseDelay);
        }
    }

    private clearAutoClose(): void {
        if (this.autoCloseTimeout) {
            clearTimeout(this.autoCloseTimeout);
            this.autoCloseTimeout = undefined;
        }
    }

    onClose(): void {
        this.clearAutoClose();
        this.close.emit();
    }

    onOverlayClick(event: Event): void {
        if (event.target === event.currentTarget) {
            this.onClose();
        }
    }

    get iconColor(): string {
        return this.config.color;
    }

    get titleColor(): string {
        return this.config.color;
    }
}
