import { CommonModule } from '@angular/common';
import { Component, inject } from '@angular/core';
import { MatCardModule } from '@angular/material/card';
import { Router } from '@angular/router';
import { GenericFormComponent, IFormField } from '@shared/components/generic';
import { TenantChangeComponent } from './tenant-change/tenant-change.component';
import { AutenticacaoController } from '../../controller/autenticacao.controller';
import { ThemeSettingsController } from '@app/portal/layout/controller/themeSettings.controller';
import { MatButtonModule } from '@angular/material/button';
import { MessageController } from '@shared/controller/message.controller';
import { LoadingComponent } from '@shared/components/generic/loading/loading.component';

@Component({
    selector: 'app-login',
    imports: [
        CommonModule,
        MatCardModule,
        GenericFormComponent,
        TenantChangeComponent,
        MatButtonModule,
        LoadingComponent,
    ],
    templateUrl: './login.component.html',
    styleUrl: './login.component.scss',
})
export class LoginComponent {
    themeSettingsController = inject(ThemeSettingsController);
    autenticacaoController = inject(AutenticacaoController);
    messageController = inject(MessageController);
    router = inject(Router);

    formFields: IFormField[] = [
        {
            name: 'email',
            label: 'Email',
            type: 'email',
            required: true,
            placeholder: '<EMAIL>',
            col: 12,
        },
    ];

    constructor() {}

    acessoParaUsuarioAdministrativo() {
        return this.router.url.includes('login-admin');
    }

    handleFormSubmit(formData: any): void {
        const loginAdmin = this.acessoParaUsuarioAdministrativo();
        this.autenticacaoController.verifyEmail(
            formData.email,
            loginAdmin,
            (mensagemErro) => this.errorLogin(mensagemErro)
        );
    }

    errorLogin(mensagem: string) {
        // Para o fluxo de login, usar modal de erro
        this.messageController.showError(mensagem, true);
    }
}
