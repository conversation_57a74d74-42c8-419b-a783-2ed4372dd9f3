.custom-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.custom-modal-container {
    background: white;
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    text-align: center;
    max-width: 400px;
    width: 90%;
    animation: slideIn 0.4s ease-out;
}

.custom-icon {
    margin-bottom: 20px;
}

.icon-container {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: block;
    margin: 0 auto 20px;
    animation: scale 0.3s ease-in-out 0.4s both;
    position: relative;
}

.icon-circle {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    position: relative;
    border: 3px solid;
    animation: pulse 0.6s ease-in-out forwards;
    transition: all 0.3s ease;
}

.custom-message {
    h3 {
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 10px 0;
        animation: fadeInUp 0.5s ease-out 0.5s both;
        transition: color 0.3s ease;
    }

    p {
        color: #666;
        font-size: 16px;
        margin: 0;
        line-height: 1.5;
        animation: fadeInUp 0.5s ease-out 0.6s both;
    }
}

// Animações
@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes scale {
    0% {
        transform: scale(0.8);
        opacity: 0;
    }
    50% {
        transform: scale(1.1);
        opacity: 0.8;
    }
    100% {
        transform: scale(1);
        opacity: 1;
    }
}

@keyframes pulse {
    0% {
        box-shadow: 0 0 0 0 rgba(108, 117, 125, 0.4);
    }
    70% {
        box-shadow: 0 0 0 10px rgba(108, 117, 125, 0);
    }
    100% {
        box-shadow: 0 0 0 0 rgba(108, 117, 125, 0);
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

// Variações de cores para diferentes tipos
.icon-circle {
    // Cores dinâmicas aplicadas via style binding
    &.success {
        border-color: #4CAF50;
        background-color: #4CAF50;
    }
    
    &.error {
        border-color: #f44336;
        background-color: #f44336;
    }
    
    &.warning {
        border-color: #ff9800;
        background-color: #ff9800;
    }
    
    &.info {
        border-color: #2196F3;
        background-color: #2196F3;
    }
    
    &.default {
        border-color: #6c757d;
        background-color: #6c757d;
    }
}

// Responsivo
@media (max-width: 480px) {
    .custom-modal-container {
        padding: 30px 20px;
        margin: 20px;
    }

    .icon-container {
        width: 60px;
        height: 60px;
    }

    .icon-circle {
        width: 60px;
        height: 60px;
        
        i {
            font-size: 2.5rem !important;
        }
    }

    .custom-message {
        h3 {
            font-size: 20px;
        }

        p {
            font-size: 14px;
        }
    }
}

// Tema escuro
@media (prefers-color-scheme: dark) {
    .custom-modal-container {
        background: #2d3748;
        color: #e2e8f0;
    }

    .custom-message p {
        color: #a0aec0;
    }
}
