import {
    Component,
    Input,
    Output,
    EventEmitter,
    signal,
    Signal,
} from '@angular/core';
import { CommonModule } from '@angular/common';

@Component({
    selector: 'app-success-modal',
    imports: [CommonModule],
    templateUrl: './success-modal.component.html',
    styleUrl: './success-modal.component.scss',
    standalone: true,
})
export class SuccessModalComponent {
    @Input() isOpen: Signal<boolean> = signal<boolean>(false);
    @Input() message: string = 'Operação realizada com sucesso!';
    @Input() autoCloseDelay: number = 2000; // 2 segundos por padrão
    @Output() close = new EventEmitter<void>();

    private autoCloseTimeout?: number;

    ngOnChanges(): void {
        if (this.isOpen()) {
            this.startAutoClose();
        } else {
            this.clearAutoClose();
        }
    }

    ngOnDestroy(): void {
        this.clearAutoClose();
    }

    private startAutoClose(): void {
        this.clearAutoClose();
        this.autoCloseTimeout = window.setTimeout(() => {
            this.onClose();
        }, this.autoCloseDelay);
    }

    private clearAutoClose(): void {
        if (this.autoCloseTimeout) {
            clearTimeout(this.autoCloseTimeout);
            this.autoCloseTimeout = undefined;
        }
    }

    onClose(): void {
        this.clearAutoClose();
        this.close.emit();
    }

    onOverlayClick(event: Event): void {
        if (event.target === event.currentTarget) {
            this.onClose();
        }
    }
}
