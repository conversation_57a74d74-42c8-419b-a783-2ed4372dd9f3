<div
    class="error-modal-overlay"
    *ngIf="isOpen()"
    (click)="onOverlayClick($event)"
>
    <div class="error-modal-container">
        <div class="error-modal-content">
            <div class="error-icon">
                <div class="error-mark">
                    <div class="error-circle flex justify-center items-center">
                        <i
                            class="material-symbols-outlined text-white text-7xl!"
                            >close</i
                        >
                    </div>
                </div>
            </div>
            <div class="error-message">
                <h3>Erro!</h3>
                <p>{{ message }}</p>
            </div>
        </div>
    </div>
</div>
