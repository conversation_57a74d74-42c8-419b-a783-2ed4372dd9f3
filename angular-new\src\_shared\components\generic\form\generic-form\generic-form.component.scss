.form-fields {
    display: flex;
    flex-wrap: wrap;
    gap: 16px;
    margin-bottom: 24px;

    .form-field {
        margin-bottom: 8px;

        &:not([class*="col-"]) {
            width: 100%;
        }

        @for $i from 1 through 12 {
            &.col-#{$i} {
                width: calc(#{percentage($i/12)} - 16px);
            }
        }

        .checkbox-field {
            padding-top: 16px;
        }

        mat-form-field {
            width: 100%;
        }
    }
}

.form-actions {
    display: flex;
    justify-content: flex-end;
    gap: 16px;
    margin-top: 16px;
}

@media (max-width: 768px) {
    .form-fields {
        .form-field {
            width: 100% !important;
        }
    }
}

.block {
    width: 100% !important;
}
