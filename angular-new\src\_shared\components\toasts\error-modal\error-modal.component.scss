.error-modal-overlay {
    position: fixed;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-color: rgba(0, 0, 0, 0.5);
    display: flex;
    justify-content: center;
    align-items: center;
    z-index: 9999;
    animation: fadeIn 0.3s ease-out;
}

.error-modal-container {
    background: white;
    border-radius: 16px;
    padding: 40px;
    box-shadow: 0 20px 60px rgba(0, 0, 0, 0.2);
    text-align: center;
    max-width: 400px;
    width: 90%;
    animation: slideIn 0.4s ease-out;
}

.error-icon {
    margin-bottom: 20px;
}

.error-mark {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    display: block;
    stroke-width: 3;
    stroke: #f44336;
    stroke-miterlimit: 10;
    margin: 0 auto 20px;
    box-shadow: inset 0px 0px 0px #f44336;
    animation: fill 0.4s ease-in-out 0.4s forwards,
        scale 0.3s ease-in-out 0.9s both;
    position: relative;
}

.error-circle {
    stroke-dasharray: 166;
    stroke-dashoffset: 166;
    stroke-width: 3;
    stroke-miterlimit: 10;
    stroke: #f44336;
    fill: none;
    animation: stroke 0.6s cubic-bezier(0.65, 0, 0.45, 1) forwards;
    width: 80px;
    height: 80px;
    border-radius: 50%;
    position: relative;
    border: 3px solid #f44336;
}

.error-message {
    h3 {
        color: #f44336;
        font-size: 24px;
        font-weight: 600;
        margin: 0 0 10px 0;
        animation: fadeInUp 0.5s ease-out 0.5s both;
    }

    p {
        color: #666;
        font-size: 16px;
        margin: 0;
        line-height: 1.5;
        animation: fadeInUp 0.5s ease-out 0.6s both;
    }
}

@keyframes fadeIn {
    from {
        opacity: 0;
    }
    to {
        opacity: 1;
    }
}

@keyframes slideIn {
    from {
        transform: translateY(-50px) scale(0.9);
        opacity: 0;
    }
    to {
        transform: translateY(0) scale(1);
        opacity: 1;
    }
}

@keyframes stroke {
    100% {
        stroke-dashoffset: 0;
    }
}

@keyframes scale {
    0%,
    100% {
        transform: none;
    }
    50% {
        transform: scale3d(1.1, 1.1, 1);
    }
}

@keyframes fill {
    100% {
        box-shadow: inset 0px 0px 0px 80px #f44336;
    }
}

@keyframes error-line {
    0% {
        height: 0;
        opacity: 0;
    }
    100% {
        height: 30px;
        opacity: 1;
    }
}

@keyframes fadeInUp {
    from {
        opacity: 0;
        transform: translateY(20px);
    }
    to {
        opacity: 1;
        transform: translateY(0);
    }
}

@media (max-width: 480px) {
    .error-modal-container {
        padding: 30px 20px;
        margin: 20px;
    }

    .error-mark {
        width: 60px;
        height: 60px;
    }

    .error-circle {
        width: 60px;
        height: 60px;
    }

    .error-message {
        h3 {
            font-size: 20px;
        }

        p {
            font-size: 14px;
        }
    }
}
